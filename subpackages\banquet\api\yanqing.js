import request from '../../../utils/request'

/** 
 * 宴请 - 提交评论
**/
export const addCommentData = (data) => {
  return request.post('/api/ai/banquet/recommend/comment', data)
}

/** 
 * 宴请 - 餐厅推荐
 * type：1：我的收藏，2：查看榜单
 * lnglat：经度和维度,eg:[113.936091,22.533719]
 * price
**/
export const restaurantDataList = (params) => {
  return request.get('/api/restaurant/recommend', params)
}

/** 
 * 宴请 - 通过纬度和经度获取定位名称
**/
export const getPositionRegeo = (params) => {
  return request.get('/api/map/regeo', params)
}

/** 
 * 宴请 - 收藏
 * type：0：未收藏，1：已收藏
 * restaurant_key
**/
export const collectDataApi = (params) => {
  return request.post('/api/restaurant/collect', params)
}

/** 
 * 宴请 - 餐厅详情页
 * restaurant_key
**/
export const GoToResDetail = (params) => {
  return request.get('/api/restaurant/detail', params)
}

/** 
 * 宴请 - 宣传图
**/
export const topBannerData = (data) => {
  return request.get('/api/restaurant/banner', data)
}

/** 
 * 宴请 - 餐厅金榜
**/
export const resTopListData = (data) => {
  return request.get('/api/restaurant/top', data)
}

/** 
 * 宴请 - 宾客档案
**/
export const getGuestDatelist = (params) => {
  return request.get('/api/restaurant/guest', params)
}

/** 
 * 宴请 - 宾客档案 - 编辑用户名
 * user_key 
 * alias
**/
export const editUserName = (params) => {
  return request.post('/api/user/alias', params)
}

/** 
 * 我的 - 消息中心
**/
export const meassgeFamilyInfo = (params) => {
  return request.get('/api/user/messages', params)
}