/* 功能导航区域组件样式 */

/* 主容器样式 */
.function-nav {
  box-sizing: border-box;
  width: 100%;
  height: 424rpx;  /* 设置固定高度，与原页面保持一致 */
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  touch-action: none;
  overflow: hidden;
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: transform;
  /* 确保没有背景遮挡 */
  background: transparent;
}

/* 背景图片样式 */
.function-nav-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 424rpx;
  z-index: 0;
  object-fit: cover;
  /* 移除暗化滤镜，保持原始亮度 */
  filter: none;
  /* 优化背景渲染 */
  transform: translateZ(0);
}

/* 机器人图片样式 */
.robot {
  position: absolute;
  width: 160rpx;
  height: 160rpx;
  top: 34%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  object-fit: contain;
  /* 优化图片渲染 */
  filter: none;
}

/* 功能导航内容容器 */
.function-nav-content {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /* 启用硬件加速 */
  transform: translateZ(0);
}

/* 椭圆环形菜单样式 - 星系旋转升级版 */
.elliptical-menu {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  perspective: 1500rpx;
  perspective-origin: center 40%;
  transition: transform 0.3s ease-out;
  overflow: visible;
  /* 启用硬件加速，优化星系旋转性能 */
  will-change: transform;
  transform: translateZ(0) rotateX(8deg);
}

/* 手动滑动状态的特殊样式 */
.elliptical-menu.galaxy-rotating {
  /* 滑动时加强性能优化 */
  transform: translateZ(0) rotateX(8deg);
}

.elliptical-menu.galaxy-rotating .menu-item {
  /* 滑动时完全禁用过渡效果，确保跟手性 */
  transition: none !important;
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: transform, opacity, filter;
}

/* 惯性滑动状态的特殊样式 - 优化版本 */
.elliptical-menu.inertia-sliding {
  /* 惯性滑动时的容器样式 */
  transition: none;
  transform: translateZ(0) rotateX(8deg);
  will-change: transform;
}

.elliptical-menu.inertia-sliding .menu-item {
  /* 惯性滑动时使用平滑过渡，增强视觉效果 */
  transition: transform 0.033s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: transform;
}

/* 惯性滑动时的视觉增强 */
.elliptical-menu.inertia-sliding .menu-icon {
  filter: brightness(1.1) drop-shadow(0 2rpx 6rpx rgba(64, 224, 255, 0.2));
}

/* 优化的椭圆轨道线（减少遮挡） */
.elliptical-menu::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  height: 50%;
  border: 1rpx solid rgba(64, 224, 255, 0.15);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: simple-glow 3s ease-in-out infinite;
  pointer-events: none;
  z-index: -5;
  opacity: 0.3;
}

/* 太极阴阳背景系统 */
.taiji-background {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200rpx;
  height: 200rpx;
  transform: translate(-50%, -50%);
  z-index: -10;
  opacity: 0.08;
  pointer-events: none;
}

.taiji-container {
  width: 100%;
  height: 100%;
  animation: taiji-rotate 20s linear infinite;
  transform-origin: center;
}

.taiji-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: brightness(1.2) contrast(1.1);
}

/* 五行能量环系统 */
.wuxing-energy-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  z-index: -8;
  pointer-events: none;
}

.energy-ring {
  position: absolute;
  border-radius: 50%;
  border: 2rpx solid;
  opacity: 0.15;
  animation: energy-pulse 4s ease-in-out infinite;
}

.energy-ring-wood {
  top: 20%;
  left: 20%;
  width: 60%;
  height: 60%;
  border-color: #4CAF50;
  animation-delay: 0s;
}

.energy-ring-fire {
  top: 15%;
  left: 15%;
  width: 70%;
  height: 70%;
  border-color: #F44336;
  animation-delay: 0.8s;
}

.energy-ring-earth {
  top: 10%;
  left: 10%;
  width: 80%;
  height: 80%;
  border-color: #FF9800;
  animation-delay: 1.6s;
}

.energy-ring-metal {
  top: 5%;
  left: 5%;
  width: 90%;
  height: 90%;
  border-color: #9E9E9E;
  animation-delay: 2.4s;
}

.energy-ring-water {
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  border-color: #2196F3;
  animation-delay: 3.2s;
}

/* 星宿轨道系统 */
.constellation-system {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  z-index: -6;
  pointer-events: none;
}

.orbit-layer {
  position: absolute;
  border-radius: 50%;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.orbit-inner {
  top: 30%;
  left: 30%;
  width: 40%;
  height: 40%;
  animation: orbit-rotate 15s linear infinite;
}

.orbit-middle {
  top: 20%;
  left: 20%;
  width: 60%;
  height: 60%;
  animation: orbit-rotate 25s linear infinite reverse;
}

.orbit-outer {
  top: 10%;
  left: 10%;
  width: 80%;
  height: 80%;
  animation: orbit-rotate 35s linear infinite;
}

.star-particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  box-shadow: 0 0 6rpx rgba(255, 255, 255, 0.6);
}

/* 星粒子位置 */
.star-1 { top: 0%; left: 50%; transform: translate(-50%, -50%); }
.star-2 { top: 50%; left: 100%; transform: translate(-50%, -50%); }
.star-3 { top: 100%; left: 50%; transform: translate(-50%, -50%); }
.star-4 { top: 0%; left: 50%; transform: translate(-50%, -50%); }
.star-5 { top: 25%; left: 93%; transform: translate(-50%, -50%); }
.star-6 { top: 75%; left: 93%; transform: translate(-50%, -50%); }
.star-7 { top: 100%; left: 50%; transform: translate(-50%, -50%); }
.star-8 { top: 0%; left: 50%; transform: translate(-50%, -50%); }
.star-9 { top: 100%; left: 50%; transform: translate(-50%, -50%); }

/* 能量流转线条系统 */
.energy-flow-system {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  z-index: -7;
  pointer-events: none;
}

.energy-line {
  position: absolute;
  width: 2rpx;
  height: 100rpx;
  background: linear-gradient(to bottom, 
    transparent 0%, 
    rgba(64, 224, 255, 0.6) 50%, 
    transparent 100%);
  border-radius: 1rpx;
  animation: energy-flow 3s ease-in-out infinite;
}

.energy-line-1 {
  top: 20%;
  left: 30%;
  transform: rotate(45deg);
  animation-delay: 0s;
}

.energy-line-2 {
  top: 60%;
  left: 70%;
  transform: rotate(-30deg);
  animation-delay: 1s;
}

.energy-line-3 {
  top: 40%;
  left: 50%;
  transform: rotate(90deg);
  animation-delay: 2s;
}

/* 菜单项通用样式 - 滚动缩放版本 + 渐变动画效果 + 3D透视 */
.menu-item {
  position: absolute;
  width: 81rpx;
  height: 81rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 优化过渡效果，让近大远小变化更平滑 */
  transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    filter 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    z-index 0s;
  transform-origin: center;
  transform-style: preserve-3d;
  z-index: 2;
  cursor: pointer;
  user-select: none;
  /* 启用硬件加速，提升滑动性能 */
  will-change: transform, opacity, filter, z-index;
  /* 添加轻微的阴影，增强立体感 */
  filter: drop-shadow(0 2rpx 8rpx rgba(0, 0, 0, 0.1));
}

/* 菜单图标样式 */
.menu-icon {
  width: 101rpx;
  height: 101rpx;
  background-color: transparent;
  border-radius: 50%;
  padding: 0;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: none;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
  position: relative;
  z-index: 1;
  /* 简化图标过渡，避免抖动 */
  transition: filter 0.2s ease-out, transform 0.2s ease-out;
  /* 3D变换支持 */
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* 五行光晕效果 */
.wuxing-aura {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  pointer-events: none;
  opacity: 0.3;
  animation: wuxing-aura-pulse 3s ease-in-out infinite;
}

/* 不同五行类型的光晕颜色 */
.wuxing-wood {
  background: radial-gradient(circle, rgba(76, 175, 80, 0.3) 0%, transparent 70%);
  box-shadow: 0 0 30rpx rgba(76, 175, 80, 0.2);
}

.wuxing-fire {
  background: radial-gradient(circle, rgba(244, 67, 54, 0.3) 0%, transparent 70%);
  box-shadow: 0 0 30rpx rgba(244, 67, 54, 0.2);
}

.wuxing-earth {
  background: radial-gradient(circle, rgba(255, 152, 0, 0.3) 0%, transparent 70%);
  box-shadow: 0 0 30rpx rgba(255, 152, 0, 0.2);
}

.wuxing-metal {
  background: radial-gradient(circle, rgba(158, 158, 158, 0.3) 0%, transparent 70%);
  box-shadow: 0 0 30rpx rgba(158, 158, 158, 0.2);
}

.wuxing-water {
  background: radial-gradient(circle, rgba(33, 150, 243, 0.3) 0%, transparent 70%);
  box-shadow: 0 0 30rpx rgba(33, 150, 243, 0.2);
}

.wuxing-family {
  background: radial-gradient(circle, rgba(64, 224, 255, 0.3) 0%, transparent 70%);
  box-shadow: 0 0 30rpx rgba(64, 224, 255, 0.2);
}

/* 精确的激活状态 - 只影响当前选中项 */
.menu-item.active {
  /* 移除transform覆盖，让JS动态缩放生效，只在原有缩放基础上增加5% */
  z-index: 15 !important;
  filter: brightness(1.05) !important;
}

.menu-item.active > .menu-icon {
  filter: brightness(1.2) drop-shadow(0 0 10rpx rgba(64, 224, 255, 0.6)) !important;
  animation: enhanced-icon-glow 1.2s ease-in-out infinite !important;
}

.menu-item.active > .wuxing-aura {
  opacity: 0.7 !important;
  transform: translate(-50%, -50%) scale(1.1) !important;
  animation: enhanced-aura-pulse 1.2s ease-in-out infinite !important;
}

/* 确保非激活状态保持正常 - 移除固定缩放，允许动态缩放 */
.menu-item:not(.active) {
  /* transform: translate(-50%, -50%) scale(1); 注释掉固定缩放 */
  z-index: 2;
}

.menu-item:not(.active) > .menu-icon {
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
  animation: none;
}

.menu-item:not(.active) > .wuxing-aura {
  opacity: 0.3;
  transform: translate(-50%, -50%) scale(1);
  animation: wuxing-aura-pulse 3s ease-in-out infinite;
}

/* 增强的发光动画 */
@keyframes enhanced-icon-glow {
  0%, 100% {
    filter: brightness(1.2) drop-shadow(0 0 12rpx rgba(64, 224, 255, 0.5));
    transform: scale(1);
  }
  50% {
    filter: brightness(1.3) drop-shadow(0 0 18rpx rgba(64, 224, 255, 0.6));
    transform: scale(1.02);
  }
}

@keyframes enhanced-aura-pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.8;
  }
}

/* 强制重置状态 - 用于手机端兼容 */
.menu-item.force-reset {
  /* 移除transform和opacity覆盖，让JS重置逻辑生效 */
  z-index: 2 !important;
  filter: none !important;
}

.menu-item.force-reset > .menu-icon {
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1)) !important;
  animation: none !important;
}

.menu-item.force-reset > .wuxing-aura {
  opacity: 0.3 !important;
  transform: translate(-50%, -50%) scale(1) !important;
  animation: wuxing-aura-pulse 3s ease-in-out infinite !important;
}

/* 手机端兼容的模糊效果 - 使用透明度和阴影模拟 */
.menu-item.blurred {
  /* 不覆盖opacity，让JS计算的透明度生效，只添加模糊滤镜效果 */
  /* 使用多重阴影模拟模糊效果 */
  filter: drop-shadow(0 0 2rpx rgba(0, 0, 0, 0.3))
          drop-shadow(1rpx 1rpx 2rpx rgba(0, 0, 0, 0.2))
          drop-shadow(-1rpx -1rpx 2rpx rgba(0, 0, 0, 0.2)) !important;
  /* 不覆盖transform，让JS计算的缩放生效 */
}

.menu-item.blurred .menu-icon {
  /* 图标额外的模糊模拟效果 */
  filter: drop-shadow(0 0 1rpx rgba(0, 0, 0, 0.4))
          drop-shadow(0.5rpx 0.5rpx 1rpx rgba(0, 0, 0, 0.3))
          drop-shadow(-0.5rpx -0.5rpx 1rpx rgba(0, 0, 0, 0.3)) !important;
  /* 轻微降低对比度 */
  opacity: 0.8;
}

.menu-item.blurred .wuxing-aura {
  /* 光晕效果也要相应调整，但不强制覆盖opacity */
  filter: drop-shadow(0 0 3rpx rgba(0, 0, 0, 0.2)) !important;
}

/* 涟漪效果 */
.ripple-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 10;
}

.ripple-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  border: 2rpx solid rgba(64, 224, 255, 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple-expand 0.6s ease-out forwards;
}

.ripple-1 {
  width: 80rpx;
  height: 80rpx;
  animation-delay: 0s;
}

.ripple-2 {
  width: 80rpx;
  height: 80rpx;
  animation-delay: 0.1s;
}

.ripple-3 {
  width: 80rpx;
  height: 80rpx;
  animation-delay: 0.2s;
}

/* 生命呼吸核心 */
.life-core {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20rpx;
  height: 20rpx;
  transform: translate(-50%, -50%);
  z-index: -5;
  pointer-events: none;
}

.core-inner, .core-middle, .core-outer {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.core-inner {
  width: 20rpx;
  height: 20rpx;
  background: radial-gradient(circle, rgba(64, 224, 255, 0.8) 0%, transparent 70%);
  animation: core-breathe 2s ease-in-out infinite;
}

.core-middle {
  width: 40rpx;
  height: 40rpx;
  background: radial-gradient(circle, rgba(64, 224, 255, 0.4) 0%, transparent 70%);
  animation: core-breathe 2s ease-in-out infinite 0.3s;
}

.core-outer {
  width: 60rpx;
  height: 60rpx;
  background: radial-gradient(circle, rgba(64, 224, 255, 0.2) 0%, transparent 70%);
  animation: core-breathe 2s ease-in-out infinite 0.6s;
}

/* 动画定义 */
@keyframes simple-glow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

@keyframes taiji-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes energy-pulse {
  0%, 100% {
    opacity: 0.15;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.05);
  }
}

@keyframes orbit-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes energy-flow {
  0%, 100% {
    opacity: 0.3;
    transform: translateY(0) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-10rpx) scale(1.1);
  }
}

@keyframes wuxing-aura-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes ripple-expand {
  0% {
    width: 80rpx;
    height: 80rpx;
    opacity: 0.8;
  }
  100% {
    width: 200rpx;
    height: 200rpx;
    opacity: 0;
  }
}

@keyframes core-breathe {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* 简化版模糊效果 - 适用于性能较低的设备 */
@media (max-width: 750px) {
  .menu-item.blurred {
    /* 简化的模糊效果，不覆盖JS计算的opacity和transform */
    filter: drop-shadow(0 0 2rpx rgba(0, 0, 0, 0.4)) !important;
  }

  .menu-item.blurred .menu-icon {
    /* 简化的图标效果 */
    opacity: 0.7;
    filter: none !important;
  }

  .menu-item.blurred .wuxing-aura {
    /* 简化的光晕效果，不覆盖opacity */
    filter: none !important;
  }
}
