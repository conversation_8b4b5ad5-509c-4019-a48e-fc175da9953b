<view class="message-pubox mt20" bindscrolltolower="loadMore" lower-threshold="100">
  <view class="message-pad">
    <view class="message-item flex mt40" wx:for="{{familyNewsData}}" wx:key="index">
      <view class="message-item-lef flex">
        <view class="message-avs family-avs flex"><image src="{{item.avatar}}" mode="aspectFit"/></view>
        <view class="message-asinfo">
          <view class="message-tit f30 black bold">{{item.nickname}}</view>
          <view class="message-tip f24 qgray mt10">{{item.content}}</view>
        </view>
      </view>
      <view class="message-time f24 qgray">{{item.send_datetime}}</view>
    </view>
  </view>

  <!-- no data -->
  <view class="guest-no-data" wx:if="{{familyNewsData.length === 0 && !isLoading}}">
    <image src="/image/groupmeal/nodata.png" mode="aspectFit"/>
    <view class="nodata-text f24 qgray mt10">未匹配到数据</view>
  </view>

  <!-- loading -->
  <view class="guest-no-data guestload" wx:if="{{isLoading}}">
    <view class="nodata-text f24 qgray mt10">加载中...</view>
  </view>
  
  <!-- to bottom -->
  <view class="guest-no-data guestload" wx:if="{{noMoreData && familyNewsData.length > 10}}">
    <view class="nodata-text f24 qgray mt10">—— 已经到底啦 ——</view>
  </view>

</view>