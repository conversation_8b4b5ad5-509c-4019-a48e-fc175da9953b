import { meassgeFamilyInfo } from '../../api/yanqing'
import { formatTime } from '@utils/formatTime'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    is_read:'',
    familyNewsData:[],

    // loadmore
    pageIndex: 1,   
    pageSize: 10,
    isLoading: false,
    noMoreData: false,
    isError: false // 新增错误状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getMeassgeData();
  },

  getMeassgeData(){
    const params = {
      page: this.data.pageIndex,
      limit: this.data.pageSize
    }
    meassgeFamilyInfo(params)
    .then( res => {
      if(res && res.code === 200){   
        const newData = res.data;

        // 格式化时间
        const formattedList = newData.map(item => {
          return {
            ...item,
            send_datetime: formatTime(item.send_datetime)
          };
        });
        console.log('formattedList:',formattedList);
        if (formattedList.length > 0) {
          this.setData({
            familyNewsData: [...this.data.familyNewsData, ...formattedList],
            pageIndex: this.data.pageIndex + 1,
            // 如果返回的数据少于请求的数量，可能没有更多数据了
            noMoreData: formattedList.length < this.data.pageSize,
            isLoading: true
          });
        } else {
          this.setData({
            noMoreData: true,
            isLoading: false
          });
        }

      }
    })
    .catch(error => {
      console.log('获取失败：',error)
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({ noMoreData: false });
    this.getMeassgeData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {    
    this.getMeassgeData();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})