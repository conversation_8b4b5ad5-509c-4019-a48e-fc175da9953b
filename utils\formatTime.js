// utils/formatTime.js
function formatTime(timestamp) {
  const date = new Date(timestamp);
  const now = new Date();
  
  // 今天 00:00:00 的时间戳
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
  // 昨天 00:00:00 的时间戳
  const yesterday = today - 24 * 60 * 60 * 1000;
  
  if (date.getTime() >= today) {
    // 今天的时间
    return `今天 ${formatHourMinute(date)}`;
  } else if (date.getTime() >= yesterday) {
    // 昨天的时间
    return `昨天 ${formatHourMinute(date)}`;
  } else {
    // 更早的时间
    return formatFullDate(date);
  }
}

function formatHourMinute(date) {
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  return `${hour}:${minute}`;
}

function formatFullDate(date) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

module.exports = {
  formatTime
};