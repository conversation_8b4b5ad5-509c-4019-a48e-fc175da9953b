
/* 为有 tabbar 的页面添加底部安全间距 */
.page-with-tabbar {
  padding-bottom: calc(48px + env(safe-area-inset-bottom));
} 

/* public fon begin */
.flex{
  display: flex;
  align-items: center;
}
.mt10{
  margin-top: 10rpx;
}
.mt15{
  margin-top: 15rpx;
}
.mt20{
  margin-top: 20rpx;
}
.mt25{
  margin-top: 25rpx;
}
.mt30{
  margin-top: 30rpx;
}
.mt35{
  margin-top: 35rpx;
}
.mt40{
  margin-top: 40rpx;
}
.mt45{
  margin-top: 45rpx;
}
.mt50{
  margin-top: 50rpx;
}
.mt55{
  margin-top: 55rpx;
}
.mt60{
  margin-top: 60rpx;
}
.mt70{
  margin-top: 70rpx;
}
.mt80{
  margin-top: 80rpx;
}
.f22{
  font-size: 22rpx;
}
.f24{
  font-size: 24rpx;
}
.f26{
  font-size: 26rpx;
}
.f28{
  font-size: 28rpx;
}
.f30{
  font-size: 30rpx;
}
.f32{
  font-size: 32rpx;
}
.f34{
  font-size: 34rpx;
}
.f36{
  font-size: 36rpx;
}
.black{
  color: #000;
}
.liang{
  color: #79AA6B;
}
.qgray{
  color: #999;
}
.gray{
  color: #666;
}
.sgray{
  color: #333;
}
.bold{
  font-weight: bold;
}
.scrollarea {
  flex: 1;
  overflow-y: hidden;
}
.whitebox{
  background: #fff;
  padding: 32rpx;
  box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(0,0,0,0.01);
  border-radius: 16rpx;
}
/* pulic fon end*/
